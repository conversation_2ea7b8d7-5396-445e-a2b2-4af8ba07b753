from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
from .models import CartProxy, CartItemProxy
from apps.cart.models import Cart, CartItem


class CartAnalyticsService:
    """Service for cart analytics and reporting"""
    
    @staticmethod
    def get_cart_analytics(days=30):
        """Get comprehensive cart analytics"""
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Base queryset for the time period
        carts = CartProxy.objects.filter(created_at__gte=cutoff_date)
        
        # Basic counts
        total_carts = carts.count()
        active_carts = carts.filter(cart_items__isnull=False).distinct().count()
        anonymous_carts = carts.filter(customer__isnull=True).count()
        registered_carts = carts.filter(customer__isnull=False).count()
        
        # Abandoned carts calculation
        abandoned_threshold = timezone.now() - timedelta(hours=24)
        abandoned_carts = 0
        
        for cart in carts.prefetch_related('cart_items'):
            if cart.cart_items.exists():
                last_activity = cart.get_last_activity()
                if last_activity and last_activity < abandoned_threshold:
                    abandoned_carts += 1
        
        # Calculate values
        cart_values = []
        item_counts = []
        
        for cart in carts.prefetch_related('cart_items__product_variant'):
            items = cart.cart_items.all()
            if items:
                total_value = sum(item.get_total_item_price() for item in items)
                cart_values.append(total_value)
                item_counts.append(len(items))
        
        average_cart_value = sum(cart_values) / len(cart_values) if cart_values else Decimal('0.00')
        average_items_per_cart = sum(item_counts) / len(item_counts) if item_counts else 0
        total_cart_value = sum(cart_values)
        
        # Abandonment rate
        abandonment_rate = (abandoned_carts / total_carts * 100) if total_carts > 0 else 0
        
        # Conversion insights
        conversion_insights = CartAnalyticsService._get_conversion_insights(carts)
        
        return {
            'total_carts': total_carts,
            'active_carts': active_carts,
            'abandoned_carts': abandoned_carts,
            'anonymous_carts': anonymous_carts,
            'registered_carts': registered_carts,
            'average_cart_value': round(average_cart_value, 2),
            'average_items_per_cart': round(average_items_per_cart, 2),
            'total_cart_value': round(total_cart_value, 2),
            'abandonment_rate': round(abandonment_rate, 2),
            'conversion_insights': conversion_insights
        }
    
    @staticmethod
    def _get_conversion_insights(carts):
        """Get conversion insights from cart data"""
        # Top products in carts
        top_products = (
            CartItemProxy.objects
            .filter(cart__in=carts)
            .values('product__title')
            .annotate(
                count=Count('id'),
                total_quantity=Sum('quantity')
            )
            .order_by('-count')[:10]
        )
        
        # Cart value distribution
        cart_values = []
        for cart in carts.prefetch_related('cart_items__product_variant'):
            items = cart.cart_items.all()
            if items:
                total_value = sum(item.get_total_item_price() for item in items)
                cart_values.append(float(total_value))
        
        value_ranges = {
            '0-50': len([v for v in cart_values if 0 <= v < 50]),
            '50-100': len([v for v in cart_values if 50 <= v < 100]),
            '100-200': len([v for v in cart_values if 100 <= v < 200]),
            '200-500': len([v for v in cart_values if 200 <= v < 500]),
            '500+': len([v for v in cart_values if v >= 500]),
        }
        
        return {
            'top_products_in_carts': list(top_products),
            'cart_value_distribution': value_ranges,
            'peak_cart_creation_hours': CartAnalyticsService._get_peak_hours(carts)
        }
    
    @staticmethod
    def _get_peak_hours(carts):
        """Get peak hours for cart creation"""
        hour_counts = {}
        for cart in carts:
            hour = cart.created_at.hour
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        # Return top 5 peak hours
        return sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:5]


class CartManagementService:
    """Service for cart management operations"""
    
    @staticmethod
    def get_abandoned_carts(days_threshold=1):
        """Get abandoned carts older than threshold"""
        threshold_date = timezone.now() - timedelta(days=days_threshold)
        
        abandoned_carts = []
        carts = CartProxy.objects.filter(
            cart_items__isnull=False
        ).distinct().prefetch_related('cart_items', 'customer__user')
        
        for cart in carts:
            last_activity = cart.get_last_activity()
            if last_activity and last_activity < threshold_date:
                abandoned_carts.append(cart)
        
        return abandoned_carts
    
    @staticmethod
    def bulk_delete_carts(cart_ids, performed_by, reason=None):
        """Bulk delete carts"""
        deleted_count = 0
        errors = []
        
        for cart_id in cart_ids:
            try:
                cart = CartProxy.objects.get(id=cart_id)
                cart.delete()
                deleted_count += 1
            except CartProxy.DoesNotExist:
                errors.append(f"Cart {cart_id} not found")
            except Exception as e:
                errors.append(f"Error deleting cart {cart_id}: {str(e)}")
        
        return {
            'deleted_count': deleted_count,
            'errors': errors,
            'total_requested': len(cart_ids)
        }
    
    @staticmethod
    def bulk_clear_carts(cart_ids, performed_by, reason=None):
        """Bulk clear cart items"""
        cleared_count = 0
        errors = []
        
        for cart_id in cart_ids:
            try:
                cart = CartProxy.objects.get(id=cart_id)
                cart.cart_items.all().delete()
                cleared_count += 1
            except CartProxy.DoesNotExist:
                errors.append(f"Cart {cart_id} not found")
            except Exception as e:
                errors.append(f"Error clearing cart {cart_id}: {str(e)}")
        
        return {
            'cleared_count': cleared_count,
            'errors': errors,
            'total_requested': len(cart_ids)
        }
    
    @staticmethod
    def export_cart_data(cart_ids):
        """Export cart data for analysis"""
        carts = CartProxy.objects.filter(
            id__in=cart_ids
        ).prefetch_related(
            'cart_items__product',
            'cart_items__product_variant',
            'customer__user'
        )
        
        export_data = []
        for cart in carts:
            cart_data = {
                'cart_id': str(cart.id),
                'customer_email': cart.customer.user.email if cart.customer else None,
                'customer_name': f"{cart.customer.first_name} {cart.customer.last_name}" if cart.customer else "Anonymous",
                'created_at': cart.created_at.isoformat(),
                'total_value': sum(item.get_total_item_price() for item in cart.cart_items.all()),
                'items_count': cart.cart_items.count(),
                'is_abandoned': cart.is_abandoned(),
                'items': [
                    {
                        'product_title': item.product.title,
                        'variant_sku': item.product_variant.sku,
                        'quantity': item.quantity,
                        'unit_price': float(item.product_variant.price),
                        'total_price': float(item.get_total_item_price()),
                    }
                    for item in cart.cart_items.all()
                ]
            }
            export_data.append(cart_data)
        
        return export_data
